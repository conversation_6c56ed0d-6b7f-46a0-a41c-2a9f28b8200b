page {
  height: 100vh;
  background: #f7f8fa;
}
.home {
  display: flex;
  flex-direction: column;
}
.top-bg-img {
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}
.my-home {
  height: 100%;
  padding: 40rpx 40rpx 24rpx 40rpx;
  box-sizing: border-box;
  position: relative;
  .header {
    justify-content: space-between;

    .left-area {
      font-weight: 400;
      font-size: 26rpx;
      color: #919499;

      .avartar {
        width: 116rpx;
        height: 116rpx;
        border-radius: 50%;
      }

      .text-area {
        margin-left: 32rpx;

        .name {
          color: #22242e;
          font-size: 40rpx;
          font-weight: 600;
          margin-bottom: 4rpx;
        }

        .login-name {
          color: #22242e;
          font-size: 36rpx;
          font-weight: 500;
        }
      }
    }

    .right-box {
      background: rgba(255, 255, 255, 0.6);
      border: 1rpx solid #ffffff;
      border-radius: 34rpx;
      box-sizing: border-box;
      width: 176rpx;
      height: 66rpx;
      color: #3c3d42;
      font-size: 24rpx;
      color: #3c3d42;
      justify-content: center;
    }
  }
  .flex-item {
    display: flex;
    align-items: center;
  }

  .card-box {
    margin-top: 48rpx;
    width: 100%;
    background: #ffffff;
    padding: 32rpx 40rpx;
    border-radius: 16rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    .title {
      font-size: 32rpx;
      color: #22242e;
      font-weight: 600;
      margin-bottom: 40rpx;
    }
    .bottom-area {
      flex: 1;
      width: 100%;
      display: flex;
      align-items: center;
      .list-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        .num {
          font-size: 40rpx;
          color: #3c3d42;
          font-weight: 600;
          font-family: "DINBold";
          margin-bottom: 24rpx;
        }
        .text {
          color: #3c3d42;
          font-size: 24rpx;
        }
      }
      .has-line {
        position: relative;
        &::after {
          content: "";
          position: absolute;
          right: 0;
          top: 30rpx;
          width: 2rpx;
          height: 48rpx;
          background: rgba(194, 197, 204, 0.4);
        }
      }
    }
  }
  .resume-box {
    width: 100%;
    position: relative;
    z-index: 1;
    margin-top: 24rpx;
    border-radius: 16rpx;
    padding: 32rpx 40rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      flex: 1;
      .title-area {
        display: flex;
        align-items: center;
        margin-bottom: 8rpx;
        .text {
          font-size: 32rpx;
          color: #22242e;
          font-weight: 600;
        }
        .img {
          width: 32rpx;
          height: 32rpx;
        }
      }
      .sub-desc {
        font-size: 22rpx;
        color: #919499;
      }
    }
    .right {
      margin-left: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }
    .box-image {
      width: 100%;
      position: absolute;
      left: 0;
      top: 0;
      z-index: -1;
    }
  }
  .resume-area {
    margin-top: 40rpx;
    padding: 20rpx 24rpx 20rpx 76rpx;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;
    .resume-bg {
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: -1;
      top: 0;
      left: 0;
    }
    .circle-box {
      position: absolute;
      top: 26rpx;
      left: 24rpx;
    }
    .left-text {
      font-size: 26rpx;
      color: #3c3d42;
    }
    .right-btn {
      width: 130rpx;
      height: 48rpx;
      background: rgba(255, 106, 77, 0.8);
      border-radius: 12rpx;
      color: #ffffff;
      font-size: 22rpx;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
.sticky-header {
  width: 100%;
  position: sticky;
  top: 0;
  left: 0;
  z-index: 999;
}
.tab-area {
  width: 100%;
  position: sticky;
  left: 0;
  z-index: 9999;
  background: #ffffff;
  .tab-main-area {
    width: 100%;
    display: flex;
    align-items: center;
    .tab-item {
      flex: 1;
      padding: 26rpx 0 38rpx 0;
      box-sizing: border-box;
      position: relative;
      .title {
        color: #666666;
        font-size: 28rpx;
        text-align: center;
      }
      &.active {
        .title {
          color: #22242e;
          font-size: 32rpx;
          font-weight: 600;
        }
        &::after {
          content: "";
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          bottom: 24rpx;
          width: 40rpx;
          height: 6rpx;
          background: #e60003;
          border-radius: 4rpx;
        }
      }
    }
  }

  .sub-tab-area {
    width: 100%;
    display: flex;
    align-items: center;
    .sub-tab-item {
      flex: 1;
      font-size: 28rpx;
      color: #666666;
      position: relative;
      padding-bottom: 40rpx;
      text-align: center;
      &:last-of-type {
        &::after {
          display: none;
        }
      }
      &::after {
        content: "";
        position: absolute;
        width: 2rpx;
        height: 40rpx;
        background: #ebecf0;
        right: 0;
        top: 0;
      }
      &.active {
        color: #e60003;
        font-size: 28rpx;
        font-weight: 600;
      }
    }
  }
}
.tips-box {
  width: 100%;
  background: #ffffff;
  .contain-area {
    width: 100%;
    padding: 16rpx 24rpx 16rpx 32rpx;
    box-sizing: border-box;
    background: rgba(255, 106, 77, 0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .text {
    color: #ff6a4d;
    font-size: 24rpx;
  }
  .close-img {
    width: 24rpx;
    height: 24rpx;
  }
}

.foot-area {
  .list-area {
    padding: 0 32rpx 60rpx 32rpx;
    box-sizing: border-box;
    .list-item {
      margin-top: 32rpx;
      .time-text {
        color: #919499;
        font-size: 24rpx;
        margin-bottom: 32rpx;
      }
    }
  }
}
.focus-area {
  .list-area {
    padding: 0 32rpx 60rpx 32rpx;
    box-sizing: border-box;
    .list-item {
      margin-top: 32rpx;
      .title-area {
        display: flex;
        align-items: center;
        margin-bottom: 32rpx;
        position: relative;
        .arrow-down {
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%) rotate(0deg);
          width: 32rpx;
          height: 32rpx;
          transition: all 0.3s;
        }
        .img {
          width: 6rpx;
          height: 24rpx;
          margin-right: 16rpx;
        }
      }
      .title-text {
        color: #919499;
        font-size: 24rpx;
      }
    }
  }
}
// 职位列表容器
.job-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.bg-white {
  background: #ffffff;
}
.mt32 {
  margin-top: 32rpx;
}
.compare-ball {
  position: fixed;
  bottom: 230rpx;
  right: 0;
  width: 104rpx;
  height: 92rpx;
  border-radius: 46rpx 0 0 46rpx;
  background: #ffffff;
  box-shadow: 0 8rpx 24rpx 2rpx rgba(34, 36, 46, 0.15);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .img-area {
    position: relative;
    width: 40rpx;
    height: 40rpx;
    .img {
      width: 100%;
      height: 100%;
    }
    .message {
      position: absolute;
      top: -8rpx;
      right: -34rpx;
      font-weight: 600;
      font-size: 20rpx;
      line-height: 28rpx;
      color: #ffffff;
      background: #e60003;
      border-radius: 18rpx;
      padding: 2rpx 14rpx;
      box-sizing: border-box;
      border: 2rpx solid #ffffff;
    }
  }
  .text {
    color: #919499;
    font-size: 20rpx;
  }
}
.expanded {
  .arrow-down {
    transform: translateY(-50%) rotate(180deg) !important;
  }
}
