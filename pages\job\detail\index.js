const ROUTER = require("@/services/mpRouter")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const APP = getApp()
Page({
  /**
   * 页面的初始数据
   */
  data: {
    selectedSubTabs: 0, // 选中的筛选项，默认选中第一个（单选模式）
    isFocus: false,
    detailData: {},
    currentTableData: [
      {
        year: "2024",
        zhaopinNum: "1802",
        baomingNum: "39201",
        rate: "39:1",
        score: "72.5",
      },
      {
        year: "2023",
        zhaopinNum: "1802",
        baomingNum: "39201",
        rate: "39:1",
        score: "72.5",
      },
      {
        year: "2022",
        zhaopinNum: "1802",
        baomingNum: "39201",
        rate: "39:1",
        score: "72.5",
      },
    ],
    isComplete: false,
    isLogin: false,
    pkListIds: [],
    id: null,
  },
  onSubTabClick(e) {
    const index = parseInt(e.currentTarget.dataset.index)
    // 单选模式，直接设置选中的索引
    this.setData({
      selectedSubTabs: index,
    })
    this.handleData(this.data.detailData.history_data.data_list, index)
  },
  // 判断是否登录
  getIsLogin() {
    const isLogin = !!APP.globalData?.userInfo?.token
    this.setData({
      isLogin: isLogin,
    })
  },
  async setFocus() {
    let focus = this.data.detailData.is_follow == 1
    const param = {
      item_type: "job",
      item_no: [this.data.detailData.id],
      type: focus ? "unfollow" : "follow",
    }
    const res = await UTIL.request(API.setFollows, param)
    if (res) {
      wx.showToast({
        title: focus ? "已取消关注" : "关注成功",
        icon: "none",
      })
      this.setData({
        ["detailData.is_follow"]: focus ? 0 : 1,
      })
    }
  },

  goComparison() {
    const newPkListIds = this.data.pkListIds
    if (!newPkListIds.includes(this.data.id)) {
      newPkListIds.push(this.data.id)
    }
    wx.setStorageSync("pkListIds", newPkListIds)
    ROUTER.navigateTo({
      path: "/pages/job/comparisonList/index",
    })
  },

  goNoticeDetail() {
    ROUTER.navigateTo({
      path: "/pages/notice/detail/index",
      query: {
        id: this.data.detailData.article_id,
      },
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      id: Number(options?.id) || null,
    })
    this.getDetail()
  },

  getPkListIds() {
    const pkListIds = wx.getStorageSync("pkListIds") || []
    this.setData({
      pkListIds,
    })
  },
  async getDetail() {
    const res = await UTIL.request(API.getJobDetail, { id: this.data.id })
    if (res?.error?.code == 0) {
      this.setData({
        detailData: res.data,
        isComplete: true,
      })
      if (res.data?.history_data?.status == 1) {
        this.handleData(res.data.history_data.data_list, 0)
      }
    }
  },

  handleData(data, index) {
    let list = []
    let note = ""
    let tableList = []
    if (data && data.length) {
      let obj = data[index]
      if (obj.list && obj.list.length) {
        list = obj.list
      }
      if (obj.column_list && obj.column_list.length) {
        tableList = obj.column_list.map((item) =>
          this.splitStringByLength(item)
        )
      }
      note = obj.note
    }
    this.setData({
      ["dataObj.list"]: list,
      ["dataObj.note"]: note,
      ["dataObj.tableList"]: tableList,
    })
  },
  splitStringByLength(str) {
    // 校验输入是否为字符串
    if (typeof str !== "string") {
      console.warn("输入必须是字符串")
      return []
    }

    const result = []
    const length = str.length

    // 当字符串长度小于5时，每2个字一组
    if (length < 5) {
      for (let i = 0; i < length; i += 2) {
        // 截取从i开始的2个字符
        result.push(str.substring(i, i + 2))
      }
    } else {
      // 当字符串长度大于等于5时，每3个字一组
      for (let i = 0; i < length; i += 3) {
        // 截取从i开始的3个字符
        result.push(str.substring(i, i + 3))
      }
    }
    return result
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    await APP.checkLoadRequest()
    await this.getIsLogin()
    this.getPkListIds()
    if (this.data.isComplete) {
      this.getDetail()
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
})
