page {
  box-sizing: border-box;
  background: rgba(247, 248, 250, 1);
}

.search {
  width: 40rpx;
  height: 40rpx;
}

.left {
  padding-left: 32rpx;
}

.img-title {
  width: 72rpx;
  height: 36rpx;
}

.notes-box {
  height: 140rpx;
  width: 100%;
  box-sizing: border-box;
  // background: rgba(255, 247, 247, 1);
  display: flex;
  align-items: center;
  padding: 0 32rpx;
  position: relative;
  margin-bottom: 24rpx;
  border-radius: 16rpx;
  .notes-bg {
    width: 100%;
    position: absolute;
    height: 100%;
    left: 0;
    border-radius: 16rpx;
  }

  .circle-text {
    font-size: 20rpx;
    color: rgba(60, 61, 66, 1);

    .num {
      font-size: 28rpx;
      font-weight: 600;
      font-family: "DINBold";
    }
  }

  .content-text {
    padding-left: 24rpx;
    flex: 1;
    position: relative;

    .title {
      font-size: 26rpx;
      color: rgba(60, 61, 66, 1);
    }

    .label {
      font-size: 22rpx;
      color: rgba(145, 148, 153, 1);
      margin-top: 4rpx;
    }
  }

  .improve-btn {
    position: relative;
    font-size: 24rpx;
    color: rgba(236, 62, 51, 1);
    width: 136rpx;
    height: 50rpx;
    border: 1px solid rgba(236, 62, 51, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12rpx;
    margin-right: 32rpx;
    box-sizing: border-box;
  }

  .close-img-box {
    position: absolute;
    top: 0;
    right: 0;
    padding-top: 8rpx;
    padding-left: 20rpx;
    padding-right: 18rpx;
  }

  .close-img {
    width: 24rpx;
    height: 24rpx;
  }
}

.menu-box {
  padding-left: 32rpx;
  margin-top: 32rpx;
}

.page-container {
  // min-height: 100vh;
  // box-sizing: border-box;
  // background: rgba(247, 248, 250, 1);
  // background-image: url(https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_collection_y.png);
  // background-repeat: no-repeat;
  // background-size: 100%;
  // display: flex;
  // flex-direction: column;

  /* 禁用滚动时的样式 */
  &.scroll-disabled {
    position: fixed;
    left: 0;
    right: 0;
    width: 100%;
    /* 保持原有的背景和样式 */
    background: rgba(247, 248, 250, 1);
    // background-image: url(https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_collection_y.png);
    background-repeat: no-repeat;
    background-size: 100%;

    /* 当弹窗显示时，控制notice-card背景图的显示 */
    .notice-list-item {
      background-image: none !important;
      background: #ffffff !important;
    }
  }
}

.main-top {
  position: sticky;
  top: var(--header-height, 100px);
  padding: 12rpx 32rpx 24rpx 32rpx;
  box-sizing: border-box;
  background: #fff;
  z-index: 1;

  // overflow: visible;
  /* 确保弹窗可以显示在容器外 */
  // background: linear-gradient(180deg, #ffffff 0%, #f7f8fa 100%);、
  &.bgf {
    background: #fff;
    // padding-top: 24rpx !important;
    border-bottom: 1rpx solid rgba(235, 236, 240, 1);
    transform: rotateZ(360deg);
  }
}

.main-content {
  padding-top: 32rpx;
  &.pt120 {
    padding-bottom: 120rpx;
  }
}

.position-list {
  padding: 32rpx;
  padding-top: 0;
}

.popu-content {
  .popu-content-c {
    padding: 0 32rpx;
    padding-top: 24rpx;
    padding-bottom: 40rpx;
    background: rgba(255, 255, 255, 1);

    .exam-list {
      display: flex;
      flex-wrap: wrap;

      // padding-top: 24rpx;
      &-item {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 212rpx;
        height: 72rpx;
        font-size: 26rpx;
        background: rgba(235, 236, 240, 0.6);
        border-radius: 12rpx;
        margin-right: 24rpx;
        margin-bottom: 24rpx;

        &:nth-child(3n) {
          margin-right: 0;
        }

        &.active {
          color: rgba(230, 0, 3, 1);
          background-color: rgba(230, 0, 3, 0.05);
        }
      }
    }
  }
}

// 覆盖 menu-filter-content 组件的样式，确保弹窗有正确的圆角
:host {
  .popup-main {
    border-radius: 24rpx 24rpx 0 0 !important;
  }
}

// 或者使用更具体的选择器
menu-filter-content .popup-main {
  border-radius: 24rpx 24rpx 0 0 !important;
}

// 如果上面的方式不行，尝试直接覆盖组件内的样式
.menu-filter-content {
  .popup-main {
    border-radius: 24rpx 24rpx 0 0 !important;
  }
}

// 地区列表样式
.region-list-container {
  padding-top: 24rpx;
  position: relative;
  padding-right: 50rpx;

  .region-list-ul {
    display: flex;
    white-space: nowrap;

    .region-list-item {
      background: rgba(235, 236, 240, 0.6);
      border-radius: 26rpx;
      font-size: 22rpx;
      height: 52rpx;
      padding: 0 32rpx;
      display: inline-flex;
      align-items: center;
      margin-right: 16rpx;

      &.active {
        background: rgba(230, 0, 3, 0.05);
        color: rgba(230, 0, 3, 1);
        font-weight: 600;
      }
    }
  }

  .right-box {
    padding-top: 24rpx;
    position: absolute;
    padding-left: 22rpx;
    background: rgba(255, 255, 255, 1);
    display: flex;
    right: 0;
    top: 0;
    height: 100%;

    .add-img {
      width: 35rpx;
      height: 35rpx;
      margin-top: 8rpx;
    }
  }
}

.group-list {
  .title {
    font-size: 28rpx;
    color: rgba(60, 61, 66, 1);
    margin-bottom: 32rpx;

    .label-text {
      font-size: 24rpx;
      color: rgba(145, 148, 153, 1);
    }
  }

  margin-bottom: 24rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .group-list-item {
    display: flex;
    flex-wrap: wrap;

    .group-item {
      margin-bottom: 24rpx;
      margin-right: 24rpx;
      background-color: rgba(235, 236, 240, 0.6);
      width: 212rpx;
      border-radius: 12rpx;
      height: 72rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 26rpx;
      color: rgba(60, 61, 66, 1);

      // border: 1rpx solid rgba(235, 236, 240, 1);
      &:nth-child(3n) {
        margin-right: 0;
      }

      &.selected {
        color: rgba(230, 0, 3, 1);
        background-color: rgba(230, 0, 3, 0.05);
        // border-color: rgba(230, 0, 3, 1);
      }
    }
  }
}

// 地区列表样式
.region-list-container {
  padding-top: 24rpx;
  position: relative;
  padding-right: 50rpx;

  .region-list-ul {
    display: flex;
    white-space: nowrap;

    .region-list-item {
      background: rgba(235, 236, 240, 0.6);
      border-radius: 26rpx;
      font-size: 22rpx;
      color: #666;
      height: 52rpx;
      padding: 0 32rpx;
      display: inline-flex;
      align-items: center;
      margin-right: 16rpx;

      &.active {
        background: rgba(230, 0, 3, 0.05);
        color: rgba(230, 0, 3, 1);
        font-weight: 600;
      }
    }
  }

  .right-box {
    padding-top: 24rpx;
    position: absolute;
    padding-left: 22rpx;
    background: rgba(255, 255, 255, 1);
    display: flex;
    right: 0;
    top: 0;
    height: 100%;

    .add-img {
      width: 35rpx;
      height: 35rpx;
      margin-top: 8rpx;
    }
  }
}
